{"augment.advanced": {"mcpServers": [{"name": "cloudflare-bindings", "command": "npx", "args": ["@cloudflare/mcp-server-cloudflare", "bindings"], "env": {"CLOUDFLARE_API_TOKEN": "****************************************"}}, {"name": "cloudflare-builds", "command": "npx", "args": ["@cloudflare/mcp-server-cloudflare", "builds"], "env": {"CLOUDFLARE_API_TOKEN": "****************************************"}}, {"name": "cloudflare-docs", "command": "npx", "args": ["@cloudflare/mcp-server-cloudflare", "docs"]}]}}